package yogu.pro.handler;


import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import yogu.pro.result.Result;

/**
 * 全局异常处理器，处理项目中抛出的业务异常
 */
//@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 捕获业务异常
     * @param ex
     * @return
     */
    @ExceptionHandler
    public Result<String> exceptionHandler(RuntimeException ex) {
        log.error("异常信息：{}", ex.getMessage());
        return Result.error("恭喜你发现了一个未发现的异常 快去联系管理员解决吧!");
    }




}
