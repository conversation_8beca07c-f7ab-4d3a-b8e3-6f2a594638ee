# ===========================================
# 通用忽略规则
# ===========================================

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*


# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.idea/
*.iml
*.ipr
*.iws
.cursorindexingignore
.specstory/*

# ===========================================
# 前端 (Frontend) 忽略规则
# ===========================================

# Node.js dependencies
frontend/node_modules/
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/pnpm-debug.log*
frontend/lerna-debug.log*

# Build outputs
frontend/dist/
frontend/dist-ssr
frontend/build/
frontend/.nuxt/
frontend/.output/
frontend/.vscode/

# Runtime data
frontend/pids
frontend/*.pid
frontend/*.seed
frontend/*.pid.lock

# Coverage directory used by tools like istanbul
frontend/coverage/
frontend/*.lcov

# nyc test coverage
frontend/.nyc_output

# Dependency directories
frontend/jspm_packages/

# Optional npm cache directory
frontend/.npm

# Optional eslint cache
frontend/.eslintcache

# Optional stylelint cache
frontend/.stylelintcache

# Microbundle cache
frontend/.rpt2_cache/
frontend/.rts2_cache_cjs/
frontend/.rts2_cache_es/
frontend/.rts2_cache_umd/

# Optional REPL history
frontend/.node_repl_history

# Output of 'npm pack'
frontend/*.tgz

# Yarn Integrity file
frontend/.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
frontend/.cache
frontend/.parcel-cache

# Next.js build output
frontend/.next

# Nuxt.js build / generate output
frontend/.nuxt
frontend/dist

# Storybook build outputs
frontend.out
frontend.storybook-out

# Temporary folders
frontend/tmp/
frontend/temp/


# Vite
frontend/dist/
frontend/.vite/

# TypeScript
frontend/*.tsbuildinfo

# Vue
frontend/dist/
frontend/.vite/

# ===========================================
# 后端 (Backend) 忽略规则
# ===========================================

# Maven build output
backend/target/
backend/*.log
backend/logs

# Maven specific files
backend/pom.xml.tag
backend/pom.xml.releaseBackup
backend/pom.xml.versionsBackup
backend/pom.xml.next
backend/release.properties
backend/dependency-reduced-pom.xml
backend/buildNumber.properties
backend/.mvn/timing.properties
backend/.mvn/wrapper/maven-wrapper.jar
