{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.38", "vue-router": "^4.3.0", "lucide-vue-next": "^0.344.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4", "tailwindcss": "^3.4.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0"}}