<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">活动管理</h1>
        <p class="text-gray-600">管理所有抽奖活动</p>
      </div>
      <router-link
        to="/activities/create"
        class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        <Plus class="w-4 h-4 mr-2" />
        创建活动
      </router-link>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">搜索活动</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="输入活动名称"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">活动状态</label>
          <select
            v-model="statusFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部状态</option>
            <option value="active">进行中</option>
            <option value="pending">未开始</option>
            <option value="ended">已结束</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">活动类型</label>
          <select
            v-model="typeFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部类型</option>
            <option value="wheel">转盘抽奖</option>
            <option value="scratch">刮刮乐</option>
            <option value="slot">老虎机</option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            重置筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 活动列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 border-b border-gray-200">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与人数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="activity in filteredActivities" :key="activity.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <Gift class="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ activity.name }}</div>
                    <div class="text-sm text-gray-500">{{ activity.description }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {{ getTypeLabel(activity.type) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(activity.status)"
                >
                  {{ getStatusLabel(activity.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ activity.participants }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>{{ activity.startDate }}</div>
                <div>{{ activity.endDate }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button
                    @click="editActivity(activity.id)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    <Edit class="w-4 h-4" />
                  </button>
                  <button
                    @click="viewActivity(activity.id)"
                    class="text-green-600 hover:text-green-900"
                  >
                    <Eye class="w-4 h-4" />
                  </button>
                  <button
                    @click="deleteActivity(activity.id)"
                    class="text-red-600 hover:text-red-900"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          上一页
        </button>
        <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg">{{ currentPage }}</span>
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Gift, Edit, Eye, Trash2 } from 'lucide-vue-next'

const router = useRouter()

const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

const activities = ref([
  {
    id: 1,
    name: '春节大抽奖',
    description: '新春佳节，好礼相送',
    type: 'wheel',
    status: 'active',
    participants: 1234,
    startDate: '2024-01-15',
    endDate: '2024-02-15'
  },
  {
    id: 2,
    name: '会员专享抽奖',
    description: '会员福利，限时参与',
    type: 'scratch',
    status: 'pending',
    participants: 567,
    startDate: '2024-02-01',
    endDate: '2024-02-28'
  },
  {
    id: 3,
    name: '新品发布抽奖',
    description: '新品上市，抽奖送礼',
    type: 'slot',
    status: 'ended',
    participants: 890,
    startDate: '2024-01-01',
    endDate: '2024-01-31'
  }
])

const filteredActivities = computed(() => {
  return activities.value.filter(activity => {
    const matchesSearch = activity.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesStatus = !statusFilter.value || activity.status === statusFilter.value
    const matchesType = !typeFilter.value || activity.type === typeFilter.value
    return matchesSearch && matchesStatus && matchesType
  })
})

const totalItems = computed(() => filteredActivities.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

const getTypeLabel = (type: string) => {
  const labels = {
    wheel: '转盘抽奖',
    scratch: '刮刮乐',
    slot: '老虎机'
  }
  return labels[type as keyof typeof labels] || type
}

const getStatusLabel = (status: string) => {
  const labels = {
    active: '进行中',
    pending: '未开始',
    ended: '已结束'
  }
  return labels[status as keyof typeof labels] || status
}

const getStatusClass = (status: string) => {
  const classes = {
    active: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    ended: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  typeFilter.value = ''
}

const editActivity = (id: number) => {
  router.push(`/activities/edit/${id}`)
}

const viewActivity = (id: number) => {
  // 查看活动详情
  console.log('查看活动:', id)
}

const deleteActivity = (id: number) => {
  if (confirm('确定要删除这个活动吗？')) {
    activities.value = activities.value.filter(activity => activity.id !== id)
  }
}
</script>