<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">奖品管理</h1>
        <p class="text-gray-600">管理所有奖品信息和库存</p>
      </div>
      <button
        @click="showAddModal = true"
        class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        <Plus class="w-4 h-4 mr-2" />
        添加奖品
      </button>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">搜索奖品</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="输入奖品名称"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">奖品类型</label>
          <select
            v-model="typeFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部类型</option>
            <option value="physical">实物奖品</option>
            <option value="virtual">虚拟奖品</option>
            <option value="coupon">优惠券</option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            重置筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 奖品列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="prize in filteredPrizes"
        :key="prize.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
      >
        <div class="aspect-w-16 aspect-h-9 bg-gray-100">
          <img
            :src="prize.image"
            :alt="prize.name"
            class="w-full h-48 object-cover"
          />
        </div>
        <div class="p-4">
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-lg font-semibold text-gray-900">{{ prize.name }}</h3>
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="getTypeClass(prize.type)"
            >
              {{ getTypeLabel(prize.type) }}
            </span>
          </div>
          <p class="text-sm text-gray-600 mb-3">{{ prize.description }}</p>
          <div class="flex items-center justify-between mb-3">
            <div class="text-sm">
              <span class="text-gray-500">库存:</span>
              <span class="font-medium" :class="prize.stock > 10 ? 'text-green-600' : 'text-red-600'">
                {{ prize.stock }}
              </span>
            </div>
            <div class="text-sm">
              <span class="text-gray-500">价值:</span>
              <span class="font-medium text-gray-900">¥{{ prize.value }}</span>
            </div>
          </div>
          <div class="flex items-center justify-between">
            <div class="text-xs text-gray-500">
              已发放: {{ prize.distributed }}
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="editPrize(prize)"
                class="p-1 text-blue-600 hover:text-blue-800"
              >
                <Edit class="w-4 h-4" />
              </button>
              <button
                @click="deletePrize(prize.id)"
                class="p-1 text-red-600 hover:text-red-800"
              >
                <Trash2 class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑奖品模态框 -->
    <div
      v-if="showAddModal || showEditModal"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click.self="closeModal"
    >
      <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
              {{ showAddModal ? '添加奖品' : '编辑奖品' }}
            </h3>
            <button
              @click="closeModal"
              class="text-gray-400 hover:text-gray-600"
            >
              <X class="w-6 h-6" />
            </button>
          </div>

          <form @submit.prevent="savePrize" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">奖品名称</label>
              <input
                v-model="currentPrize.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">奖品描述</label>
              <textarea
                v-model="currentPrize.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              ></textarea>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">奖品类型</label>
              <select
                v-model="currentPrize.type"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="physical">实物奖品</option>
                <option value="virtual">虚拟奖品</option>
                <option value="coupon">优惠券</option>
              </select>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">库存数量</label>
                <input
                  v-model="currentPrize.stock"
                  type="number"
                  min="0"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">奖品价值</label>
                <input
                  v-model="currentPrize.value"
                  type="number"
                  min="0"
                  step="0.01"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">奖品图片URL</label>
              <input
                v-model="currentPrize.image"
                type="url"
                placeholder="https://example.com/image.jpg"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="closeModal"
                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                保存
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Plus, Edit, Trash2, X } from 'lucide-vue-next'

const searchQuery = ref('')
const typeFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)

const prizes = ref([
  {
    id: 1,
    name: 'iPhone 15 Pro',
    description: '最新款苹果手机，256GB存储',
    type: 'physical',
    stock: 5,
    value: 8999,
    distributed: 2,
    image: 'https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg?auto=compress&cs=tinysrgb&w=400'
  },
  {
    id: 2,
    name: '100元优惠券',
    description: '全场通用优惠券，满200可用',
    type: 'coupon',
    stock: 100,
    value: 100,
    distributed: 45,
    image: 'https://images.pexels.com/photos/3943716/pexels-photo-3943716.jpeg?auto=compress&cs=tinysrgb&w=400'
  },
  {
    id: 3,
    name: '会员积分',
    description: '1000积分，可兑换商品',
    type: 'virtual',
    stock: 500,
    value: 10,
    distributed: 234,
    image: 'https://images.pexels.com/photos/6801648/pexels-photo-6801648.jpeg?auto=compress&cs=tinysrgb&w=400'
  }
])

const currentPrize = ref({
  id: 0,
  name: '',
  description: '',
  type: 'physical',
  stock: 0,
  value: 0,
  distributed: 0,
  image: ''
})

const filteredPrizes = computed(() => {
  return prizes.value.filter(prize => {
    const matchesSearch = prize.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesType = !typeFilter.value || prize.type === typeFilter.value
    return matchesSearch && matchesType
  })
})

const getTypeLabel = (type: string) => {
  const labels = {
    physical: '实物奖品',
    virtual: '虚拟奖品',
    coupon: '优惠券'
  }
  return labels[type as keyof typeof labels] || type
}

const getTypeClass = (type: string) => {
  const classes = {
    physical: 'bg-blue-100 text-blue-800',
    virtual: 'bg-green-100 text-green-800',
    coupon: 'bg-yellow-100 text-yellow-800'
  }
  return classes[type as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const resetFilters = () => {
  searchQuery.value = ''
  typeFilter.value = ''
}

const editPrize = (prize: any) => {
  currentPrize.value = { ...prize }
  showEditModal.value = true
}

const deletePrize = (id: number) => {
  if (confirm('确定要删除这个奖品吗？')) {
    prizes.value = prizes.value.filter(prize => prize.id !== id)
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  currentPrize.value = {
    id: 0,
    name: '',
    description: '',
    type: 'physical',
    stock: 0,
    value: 0,
    distributed: 0,
    image: ''
  }
}

const savePrize = () => {
  if (showAddModal.value) {
    // 添加新奖品
    const newId = Math.max(...prizes.value.map(p => p.id)) + 1
    prizes.value.push({
      ...currentPrize.value,
      id: newId
    })
  } else {
    // 编辑现有奖品
    const index = prizes.value.findIndex(p => p.id === currentPrize.value.id)
    if (index !== -1) {
      prizes.value[index] = { ...currentPrize.value }
    }
  }
  closeModal()
}
</script>