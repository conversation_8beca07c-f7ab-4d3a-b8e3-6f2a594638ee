<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-3xl font-bold tracking-tight">活动管理</h1>
      <p class="text-muted-foreground">创建和管理抽奖活动，设置奖品和规则</p>
    </div>

    <!-- 步骤指示器 -->
    <Card>
      <CardContent class="p-6">
        <div class="flex items-center justify-between mb-8">
          <div
            v-for="(step, index) in steps"
            :key="step.id"
            class="flex items-center"
            :class="{ 'flex-1': index < steps.length - 1 }"
          >
            <div class="flex items-center">
              <div
                class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium"
                :class="currentStep >= step.id ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'"
              >
                {{ step.id }}
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium" :class="currentStep >= step.id ? 'text-primary' : 'text-muted-foreground'">
                  {{ step.title }}
                </div>
                <div class="text-xs text-muted-foreground">{{ step.description }}</div>
              </div>
          </div>
          <div
            v-if="index < steps.length - 1"
            class="flex-1 h-0.5 mx-4"
            :class="currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'"
          ></div>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <Card>
      <CardContent class="p-6">
        <!-- 步骤1: 基础设置 -->
        <div v-if="currentStep === 1" class="space-y-6">
          <div class="flex items-center mb-6">
            <Settings class="w-5 h-5 text-muted-foreground mr-2" />
            <h2 class="text-lg font-semibold">基础设置</h2>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <span class="text-red-500">*</span> 活动类型
              </label>
              <select
                v-model="formData.activityType"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择活动类型</option>
                <option value="wheel">转盘抽奖</option>
                <option value="scratch">刮刮乐</option>
                <option value="slot">老虎机</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <span class="text-red-500">*</span> 活动形式
              </label>
              <div class="flex space-x-4">
                <label class="flex items-center">
                  <input
                    v-model="formData.activityForm"
                    type="radio"
                    value="wheel"
                    class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">转盘抽奖</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="formData.activityForm"
                    type="radio"
                    value="scratch"
                    class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">刮刮乐</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="formData.activityForm"
                    type="radio"
                    value="slot"
                    class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">老虎机</span>
                </label>
              </div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-foreground mb-2">
              <span class="text-destructive">*</span> 活动名称
            </label>
            <Input
              v-model="formData.activityName"
              type="text"
              placeholder="请输入活动名称"
              maxlength="50"
            />
            <div class="text-right text-xs text-muted-foreground mt-1">
              {{ formData.activityName.length }} / 50
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <span class="text-red-500">*</span> 活动时间
              </label>
              <div class="flex items-center space-x-2">
                <DateTimePicker
                  v-model="formData.startTime"
                  placeholder="选择开始时间"
                  class="flex-1"
                />
                <span class="text-muted-foreground">至</span>
                <DateTimePicker
                  v-model="formData.endTime"
                  placeholder="选择结束时间"
                  class="flex-1"
                />
              </div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-foreground mb-2">
              <span class="text-destructive">*</span> 活动规则
            </label>
            <Textarea
              v-model="formData.activityRules"
              placeholder="请输入活动规则说明"
              rows="6"
              maxlength="500"
            />
            <div class="text-right text-xs text-muted-foreground mt-1">
              {{ formData.activityRules.length }} / 500
            </div>
          </div>
        </div>

        <!-- 步骤2: 奖励设置 -->
        <div v-if="currentStep === 2" class="space-y-6">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
              <Gift class="w-5 h-5 text-gray-600 mr-2" />
              <h2 class="text-lg font-semibold text-gray-900">奖励设置</h2>
            </div>
            <button
              @click="addPrize"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus class="w-4 h-4 mr-2" />
              添加奖品
            </button>
          </div>

          <div class="space-y-4">
            <div
              v-for="(prize, index) in formData.prizes"
              :key="index"
              class="p-4 border border-gray-200 rounded-lg"
            >
              <div class="flex items-center justify-between mb-4">
                <h3 class="font-medium text-gray-900">奖品 {{ index + 1 }}</h3>
                <button
                  @click="removePrize(index)"
                  class="text-red-600 hover:text-red-800"
                >
                  <Trash2 class="w-4 h-4" />
                </button>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">奖品名称</label>
                  <input
                    v-model="prize.name"
                    type="text"
                    placeholder="请输入奖品名称"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">奖品数量</label>
                  <input
                    v-model="prize.quantity"
                    type="number"
                    min="1"
                    placeholder="请输入数量"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">中奖概率 (%)</label>
                  <input
                    v-model="prize.probability"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    placeholder="请输入概率"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 抽奖设置 -->
        <div v-if="currentStep === 3" class="space-y-6">
          <div class="flex items-center mb-6">
            <Target class="w-5 h-5 text-gray-600 mr-2" />
            <h2 class="text-lg font-semibold text-gray-900">抽奖设置</h2>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">每人每天抽奖次数</label>
              <input
                v-model="formData.dailyLimit"
                type="number"
                min="1"
                placeholder="请输入次数"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">总抽奖次数限制</label>
              <input
                v-model="formData.totalLimit"
                type="number"
                min="1"
                placeholder="请输入总次数"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">参与条件</label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input
                  v-model="formData.requireLogin"
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">需要登录</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="formData.requireMember"
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">仅限会员</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 步骤4: UI设置 -->
        <div v-if="currentStep === 4" class="space-y-6">
          <div class="flex items-center mb-6">
            <Palette class="w-5 h-5 text-gray-600 mr-2" />
            <h2 class="text-lg font-semibold text-gray-900">UI设置</h2>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">上传活动图片</label>
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload class="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-600">点击上传或拖拽图片到此处</p>
              <p class="text-xs text-gray-500 mt-1">支持 JPG、PNG 格式，建议尺寸 750x400px</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">主题色彩</label>
              <div class="flex space-x-2">
                <div
                  v-for="color in themeColors"
                  :key="color"
                  @click="formData.themeColor = color"
                  class="w-8 h-8 rounded-full cursor-pointer border-2"
                  :class="formData.themeColor === color ? 'border-gray-400' : 'border-gray-200'"
                  :style="{ backgroundColor: color }"
                ></div>
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">背景样式</label>
              <select
                v-model="formData.backgroundStyle"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="gradient">渐变背景</option>
                <option value="solid">纯色背景</option>
                <option value="image">图片背景</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 步骤5: 提示语设置 -->
        <div v-if="currentStep === 5" class="space-y-6">
          <div class="flex items-center mb-6">
            <MessageSquare class="w-5 h-5 text-gray-600 mr-2" />
            <h2 class="text-lg font-semibold text-gray-900">提示语设置</h2>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">中奖提示</label>
              <input
                v-model="formData.winMessage"
                type="text"
                placeholder="恭喜您中奖了！"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">未中奖提示</label>
              <input
                v-model="formData.loseMessage"
                type="text"
                placeholder="很遗憾，未中奖，请再接再厉！"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">次数用完提示</label>
              <input
                v-model="formData.limitMessage"
                type="text"
                placeholder="今日抽奖次数已用完，明天再来吧！"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
        <button
          @click="prevStep"
          :disabled="currentStep === 1"
          class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          保存草稿
        </button>
        <div class="flex space-x-3">
          <Button
            v-if="currentStep > 1"
            variant="outline"
            @click="prevStep"
          >
            上一步
          </Button>
          <Button
            v-if="currentStep < 5"
            @click="nextStep"
          >
            下一步
            <ChevronRight class="w-4 h-4 inline ml-1" />
          </Button>
          <Button
            v-if="currentStep === 5"
            @click="submitForm"
            class="bg-green-600 hover:bg-green-700"
          >
            创建活动
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Settings, Gift, Target, Palette, MessageSquare,
  Plus, Trash2, Upload, ChevronRight
} from 'lucide-vue-next'
import DateTimePicker from '@/components/ui/datetime-picker.vue'
import { Card, CardContent, Button, Input, Textarea } from '@/components/ui'

const router = useRouter()

const currentStep = ref(1)

const steps = [
  { id: 1, title: '基础设置', description: '设置活动基本信息' },
  { id: 2, title: '奖励设置', description: '配置奖品和概率' },
  { id: 3, title: '抽奖设置', description: '设置抽奖规则' },
  { id: 4, title: 'UI设置', description: '上传活动图片' },
  { id: 5, title: '提示语设置', description: '设置中奖提示' }
]

const formData = ref({
  activityType: '',
  activityForm: 'wheel',
  activityName: '',
  startTime: undefined as Date | undefined,
  endTime: undefined as Date | undefined,
  activityRules: '',
  prizes: [
    { name: '', quantity: 1, probability: 0 }
  ],
  dailyLimit: 3,
  totalLimit: 1000,
  requireLogin: true,
  requireMember: false,
  themeColor: '#3B82F6',
  backgroundStyle: 'gradient',
  winMessage: '恭喜您中奖了！',
  loseMessage: '很遗憾，未中奖，请再接再厉！',
  limitMessage: '今日抽奖次数已用完，明天再来吧！'
})

const themeColors = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
  '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
]

const nextStep = () => {
  if (currentStep.value < 5) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const addPrize = () => {
  formData.value.prizes.push({
    name: '',
    quantity: 1,
    probability: 0
  })
}

const removePrize = (index: number) => {
  if (formData.value.prizes.length > 1) {
    formData.value.prizes.splice(index, 1)
  }
}

const submitForm = () => {
  // 提交表单逻辑
  console.log('提交表单:', formData.value)
  alert('活动创建成功！')
  router.push('/activities')
}
</script>