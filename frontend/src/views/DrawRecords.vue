<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">抽奖记录</h1>
      <p class="text-gray-600">查看所有用户的抽奖参与记录</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div
        v-for="stat in recordStats"
        :key="stat.title"
        class="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ stat.value }}</p>
          </div>
          <div class="p-3 rounded-full" :class="stat.bgColor">
            <component :is="stat.icon" class="w-6 h-6" :class="stat.iconColor" />
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">搜索用户</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="输入用户名"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">活动名称</label>
          <select
            v-model="activityFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部活动</option>
            <option value="春节大抽奖">春节大抽奖</option>
            <option value="会员专享抽奖">会员专享抽奖</option>
            <option value="新品发布抽奖">新品发布抽奖</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">抽奖结果</label>
          <select
            v-model="resultFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部结果</option>
            <option value="win">中奖</option>
            <option value="lose">未中奖</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
          <select
            v-model="timeFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部时间</option>
            <option value="today">今天</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            重置筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 记录列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 border-b border-gray-200">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">抽奖结果</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖品信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="record in filteredRecords" :key="record.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <User class="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ record.username }}</div>
                    <div class="text-sm text-gray-500">{{ record.phone }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ record.activity }}</div>
                <div class="text-sm text-gray-500">{{ record.activityType }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="record.result === 'win' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                >
                  {{ record.result === 'win' ? '中奖' : '未中奖' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="record.prize" class="text-sm">
                  <div class="font-medium text-gray-900">{{ record.prize.name }}</div>
                  <div class="text-gray-500">价值: ¥{{ record.prize.value }}</div>
                </div>
                <div v-else class="text-sm text-gray-500">-</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>{{ record.date }}</div>
                <div>{{ record.time }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ record.ip }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  @click="viewRecord(record)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  <Eye class="w-4 h-4" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          上一页
        </button>
        <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg">{{ currentPage }}</span>
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 记录详情模态框 -->
    <div
      v-if="showRecordModal"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click.self="closeRecordModal"
    >
      <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="relative bg-white rounded-lg shadow-xl max-w-lg w-full p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">抽奖记录详情</h3>
            <button
              @click="closeRecordModal"
              class="text-gray-400 hover:text-gray-600"
            >
              <X class="w-6 h-6" />
            </button>
          </div>

          <div v-if="selectedRecord" class="space-y-4">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">用户名:</span>
                <span class="ml-2 text-gray-900">{{ selectedRecord.username }}</span>
              </div>
              <div>
                <span class="text-gray-500">手机号:</span>
                <span class="ml-2 text-gray-900">{{ selectedRecord.phone }}</span>
              </div>
              <div>
                <span class="text-gray-500">活动名称:</span>
                <span class="ml-2 text-gray-900">{{ selectedRecord.activity }}</span>
              </div>
              <div>
                <span class="text-gray-500">活动类型:</span>
                <span class="ml-2 text-gray-900">{{ selectedRecord.activityType }}</span>
              </div>
              <div>
                <span class="text-gray-500">抽奖结果:</span>
                <span
                  class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="selectedRecord.result === 'win' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                >
                  {{ selectedRecord.result === 'win' ? '中奖' : '未中奖' }}
                </span>
              </div>
              <div>
                <span class="text-gray-500">参与时间:</span>
                <span class="ml-2 text-gray-900">{{ selectedRecord.date }} {{ selectedRecord.time }}</span>
              </div>
              <div>
                <span class="text-gray-500">IP地址:</span>
                <span class="ml-2 text-gray-900">{{ selectedRecord.ip }}</span>
              </div>
              <div>
                <span class="text-gray-500">设备信息:</span>
                <span class="ml-2 text-gray-900">{{ selectedRecord.device }}</span>
              </div>
            </div>

            <div v-if="selectedRecord.prize" class="p-4 bg-green-50 rounded-lg">
              <h4 class="text-sm font-medium text-green-900 mb-2">中奖信息</h4>
              <div class="text-sm text-green-800">
                <div>奖品名称: {{ selectedRecord.prize.name }}</div>
                <div>奖品价值: ¥{{ selectedRecord.prize.value }}</div>
                <div>发放状态: {{ selectedRecord.prize.delivered ? '已发放' : '待发放' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { User, Activity, Award, TrendingUp, Eye, X } from 'lucide-vue-next'

const searchQuery = ref('')
const activityFilter = ref('')
const resultFilter = ref('')
const timeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showRecordModal = ref(false)
const selectedRecord = ref(null)

const recordStats = [
  {
    title: '总参与次数',
    value: '2,456',
    icon: Activity,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    title: '中奖次数',
    value: '234',
    icon: Award,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    title: '中奖率',
    value: '9.5%',
    icon: TrendingUp,
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600'
  },
  {
    title: '今日参与',
    value: '89',
    icon: User,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  }
]

const records = ref([
  {
    id: 1,
    username: '张三',
    phone: '138****1234',
    activity: '春节大抽奖',
    activityType: '转盘抽奖',
    result: 'win',
    prize: {
      name: 'iPhone 15 Pro',
      value: 8999,
      delivered: true
    },
    date: '2024-01-20',
    time: '14:30:25',
    ip: '*************',
    device: 'iPhone 13'
  },
  {
    id: 2,
    username: '李四',
    phone: '139****5678',
    activity: '会员专享抽奖',
    activityType: '刮刮乐',
    result: 'lose',
    prize: null,
    date: '2024-01-20',
    time: '13:45:12',
    ip: '*************',
    device: 'Android'
  },
  {
    id: 3,
    username: '王五',
    phone: '137****9012',
    activity: '新品发布抽奖',
    activityType: '老虎机',
    result: 'win',
    prize: {
      name: '100元优惠券',
      value: 100,
      delivered: false
    },
    date: '2024-01-19',
    time: '16:20:08',
    ip: '*************',
    device: 'iPad'
  }
])

const filteredRecords = computed(() => {
  return records.value.filter(record => {
    const matchesSearch = record.username.includes(searchQuery.value)
    const matchesActivity = !activityFilter.value || record.activity === activityFilter.value
    const matchesResult = !resultFilter.value || record.result === resultFilter.value
    // 这里可以添加时间筛选逻辑
    return matchesSearch && matchesActivity && matchesResult
  })
})

const totalItems = computed(() => filteredRecords.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

const resetFilters = () => {
  searchQuery.value = ''
  activityFilter.value = ''
  resultFilter.value = ''
  timeFilter.value = ''
}

const viewRecord = (record: any) => {
  selectedRecord.value = record
  showRecordModal.value = true
}

const closeRecordModal = () => {
  showRecordModal.value = false
  selectedRecord.value = null
}
</script>