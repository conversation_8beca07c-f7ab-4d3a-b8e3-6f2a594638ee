<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">参与记录</h1>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <!-- 活动类型 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">活动类型</label>
          <select 
            v-model="filters.activityType" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部</option>
            <option value="扫码抽奖">扫码抽奖</option>
            <option value="分享抽奖">分享抽奖</option>
            <option value="签到抽奖">签到抽奖</option>
          </select>
        </div>

        <!-- 活动ID/活动名称 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">活动ID/活动名称</label>
          <input 
            v-model="filters.activitySearch" 
            type="text" 
            placeholder="请输入"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <!-- 手机号/用户ID -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">手机号/用户ID</label>
          <input 
            v-model="filters.userSearch" 
            type="text" 
            placeholder="请输入"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button 
            @click="handleSearch"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            查询
          </button>
          <button 
            @click="handleReset"
            class="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            重置
          </button>
        </div>
        
        <button 
          @click="handleExport"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          导出
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动类型</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机号</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与结果</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与时间</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="record in records" :key="record.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.activityId }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.activityName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.activityType }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.userId }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.phoneNumber }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  :class="{
                    'px-2 py-1 text-xs font-medium rounded-full': true,
                    'bg-green-100 text-green-800': record.result === '中奖',
                    'bg-gray-100 text-gray-800': record.result === '未中奖'
                  }"
                >
                  {{ record.result }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.participationCount }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatDate(record.participationTime) }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button 
            @click="previousPage"
            :disabled="currentPage === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <button 
            @click="nextPage"
            :disabled="currentPage === totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> 到 
              <span class="font-medium">{{ Math.min(currentPage * pageSize, total) }}</span> 条，
              共 <span class="font-medium">{{ total }}</span> 条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <button 
                @click="previousPage"
                :disabled="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>
              <button 
                @click="nextPage"
                :disabled="currentPage === totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// 过滤条件
const filters = reactive({
  activityType: '',
  activitySearch: '',
  userSearch: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const totalPages = ref(0)

// 模拟数据
const records = ref([
  {
    id: 1,
    activityId: 'ACT001',
    activityName: '新年抽奖活动',
    activityType: '扫码抽奖',
    userId: 'USER001',
    phoneNumber: '138****8888',
    result: '中奖',
    participationCount: 1,
    participationTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    activityId: 'ACT002',
    activityName: '春节福利抽奖',
    activityType: '分享抽奖',
    userId: 'USER002',
    phoneNumber: '139****9999',
    result: '未中奖',
    participationCount: 3,
    participationTime: '2024-01-16 14:20:00'
  }
])

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  console.log('搜索条件:', filters)
  // TODO: 调用API进行搜索
  loadData()
}

// 重置
const handleReset = () => {
  filters.activityType = ''
  filters.activitySearch = ''
  filters.userSearch = ''
  loadData()
}

// 导出
const handleExport = () => {
  console.log('导出数据')
  // TODO: 实现导出功能
}

// 加载数据
const loadData = () => {
  // TODO: 调用API加载数据
  total.value = 100
  totalPages.value = Math.ceil(total.value / pageSize.value)
}

// 分页
const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    loadData()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    loadData()
  }
}

onMounted(() => {
  loadData()
})
</script> 