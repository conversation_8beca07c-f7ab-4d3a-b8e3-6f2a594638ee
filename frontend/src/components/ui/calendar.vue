<template>
  <div :class="cn('p-3', $attrs.class)">
    <Calendar
      v-bind="forwarded"
      :class="cn(
        'w-full border-collapse select-none space-y-1',
        $attrs.class
      )"
    />
  </div>
</template>

<script setup lang="ts">
import { Calendar, useForwardPropsEmits } from 'radix-vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: string
}>()

const emits = defineEmits<{
  'update:modelValue': [value: Date | undefined]
}>()

const forwarded = useForwardPropsEmits(props, emits)
</script>
