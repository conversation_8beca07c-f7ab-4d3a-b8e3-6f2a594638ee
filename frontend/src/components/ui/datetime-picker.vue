<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="cn(
          'w-full justify-start text-left font-normal',
          !modelValue && 'text-muted-foreground',
          $attrs.class
        )"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ modelValue ? formatDateTime(modelValue) : placeholder }}
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0">
      <div class="p-4 space-y-4">
        <Calendar
          v-model="selectedDate"
          :locale="zhCN"
          initial-focus
        />
        <div class="flex items-center space-x-2">
          <Input
            v-model="timeValue"
            type="time"
            class="flex-1"
            placeholder="选择时间"
          />
          <Button @click="confirmDateTime" size="sm">
            确认
          </Button>
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import Button from './button.vue'
import Calendar from './calendar.vue'
import Popover from './popover.vue'
import PopoverContent from './popover-content.vue'
import PopoverTrigger from './popover-trigger.vue'
import Input from './input.vue'

interface Props {
  modelValue?: Date
  placeholder?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择日期和时间'
})

const emit = defineEmits<{
  'update:modelValue': [value: Date | undefined]
}>()

const selectedDate = ref<Date>()
const timeValue = ref('')

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedDate.value = newValue
    timeValue.value = format(newValue, 'HH:mm')
  }
}, { immediate: true })

const formatDateTime = (date: Date) => {
  return format(date, 'yyyy年MM月dd日 HH:mm', { locale: zhCN })
}

const confirmDateTime = () => {
  if (selectedDate.value && timeValue.value) {
    const [hours, minutes] = timeValue.value.split(':').map(Number)
    const newDate = new Date(selectedDate.value)
    newDate.setHours(hours, minutes, 0, 0)
    emit('update:modelValue', newDate)
  }
}
</script>
