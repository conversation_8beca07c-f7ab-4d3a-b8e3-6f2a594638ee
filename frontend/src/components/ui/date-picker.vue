<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="cn(
          'w-full justify-start text-left font-normal',
          !modelValue && 'text-muted-foreground',
          $attrs.class
        )"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ modelValue ? format(modelValue, 'PPP', { locale: zhCN }) : placeholder }}
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0">
      <Calendar
        v-model="modelValue"
        :locale="zhCN"
        initial-focus
      />
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import Button from './button.vue'
import Calendar from './calendar.vue'
import Popover from './popover.vue'
import PopoverContent from './popover-content.vue'
import PopoverTrigger from './popover-trigger.vue'

interface Props {
  modelValue?: Date
  placeholder?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '选择日期'
})

const emit = defineEmits<{
  'update:modelValue': [value: Date | undefined]
}>()

const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>
