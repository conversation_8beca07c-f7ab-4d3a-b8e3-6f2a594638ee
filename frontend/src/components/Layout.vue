<template>
  <div class="min-h-screen bg-background">
    <!-- 侧边栏 -->
    <div
      class="fixed inset-y-0 left-0 z-50 bg-card border-r shadow-lg transform transition-all duration-300 ease-in-out lg:translate-x-0"
      :class="[
        { '-translate-x-full': !sidebarOpen },
        sidebarCollapsed ? 'w-16' : 'w-64'
      ]"
    >
      <div class="flex items-center justify-between h-16 px-4 border-b">
        <h1
          v-if="!sidebarCollapsed"
          class="text-xl font-bold text-foreground transition-opacity duration-200"
        >
          抽奖管理系统
        </h1>
        <Button
          variant="ghost"
          size="icon"
          @click="toggleSidebarCollapse"
          class="hidden lg:flex"
        >
          <PanelLeftClose v-if="!sidebarCollapsed" class="h-4 w-4" />
          <PanelLeftOpen v-else class="h-4 w-4" />
        </Button>
      </div>

      <nav class="mt-4 flex-1 overflow-y-auto">
        <div class="px-2 space-y-1">
          <!-- 仪表盘 -->
          <router-link
            to="/"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
            :class="{ 'bg-accent text-accent-foreground': $route.path === '/' }"
          >
            <BarChart3 class="w-5 h-5" :class="sidebarCollapsed ? '' : 'mr-3'" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-200">仪表盘</span>
          </router-link>

          <!-- 活动管理子菜单 -->
          <div v-if="!sidebarCollapsed">
            <Button
              variant="ghost"
              @click="toggleActivitySubmenu"
              class="w-full justify-between px-3 py-2 h-auto text-sm font-medium"
              :class="{ 'bg-accent text-accent-foreground': $route.path.startsWith('/activities') }"
            >
              <div class="flex items-center">
                <Gift class="w-5 h-5 mr-3" />
                <span>活动管理</span>
              </div>
              <ChevronDown
                class="w-4 h-4 transition-transform duration-200"
                :class="{ 'rotate-180': activitySubmenuOpen }"
              />
            </Button>

            <!-- 子菜单 -->
            <div v-show="activitySubmenuOpen" class="ml-6 mt-1 space-y-1">
              <router-link
                to="/activities"
                class="flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
                :class="{ 'bg-accent text-accent-foreground': $route.path === '/activities' }"
              >
                <List class="w-4 h-4 mr-3" />
                活动列表
              </router-link>
              <router-link
                to="/activities/create"
                class="flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
                :class="{ 'bg-accent text-accent-foreground': $route.path === '/activities/create' }"
              >
                <Plus class="w-4 h-4 mr-3" />
                创建活动
              </router-link>
            </div>
          </div>

          <!-- 折叠状态下的活动管理 -->
          <div v-else class="relative group">
            <router-link
              to="/activities"
              class="flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
              :class="{ 'bg-accent text-accent-foreground': $route.path.startsWith('/activities') }"
            >
              <Gift class="w-5 h-5" />
            </router-link>
            <!-- 悬浮菜单 -->
            <div class="absolute left-full top-0 ml-2 w-48 bg-popover border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <div class="p-2 space-y-1">
                <router-link
                  to="/activities"
                  class="flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
                >
                  <List class="w-4 h-4 mr-3" />
                  活动列表
                </router-link>
                <router-link
                  to="/activities/create"
                  class="flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
                >
                  <Plus class="w-4 h-4 mr-3" />
                  创建活动
                </router-link>
              </div>
            </div>
          </div>

          <!-- 参与记录 -->
          <router-link
            to="/participation-records"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
            :class="{ 'bg-accent text-accent-foreground': $route.path === '/participation-records' }"
          >
            <FileText class="w-5 h-5" :class="sidebarCollapsed ? '' : 'mr-3'" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-200">参与记录</span>
          </router-link>

          <!-- 中奖列表 -->
          <router-link
            to="/winners"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
            :class="{ 'bg-accent text-accent-foreground': $route.path === '/winners' }"
          >
            <Trophy class="w-5 h-5" :class="sidebarCollapsed ? '' : 'mr-3'" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-200">中奖列表</span>
          </router-link>

          <!-- 黑名单管理 -->
          <router-link
            to="/blacklist"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
            :class="{ 'bg-accent text-accent-foreground': $route.path === '/blacklist' }"
          >
            <Shield class="w-5 h-5" :class="sidebarCollapsed ? '' : 'mr-3'" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-200">黑名单管理</span>
          </router-link>

          <!-- 其他菜单项 -->
          <router-link
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground"
            :class="{ 'bg-accent text-accent-foreground': $route.path === item.path }"
          >
            <component :is="item.icon" class="w-5 h-5" :class="sidebarCollapsed ? '' : 'mr-3'" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-200">{{ item.label }}</span>
          </router-link>
        </div>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="transition-all duration-300" :class="sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'">
      <!-- 顶部导航栏 -->
      <header class="bg-card shadow-sm border-b">
        <div class="flex items-center justify-between px-4 py-4">
          <Button
            variant="ghost"
            size="icon"
            @click="sidebarOpen = !sidebarOpen"
            class="lg:hidden"
          >
            <Menu class="w-5 h-5" />
          </Button>

          <div class="flex items-center space-x-4 ml-auto">
            <Button variant="ghost" size="icon" class="relative">
              <Bell class="w-5 h-5" />
              <span class="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full"></span>
            </Button>
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <User class="w-4 h-4 text-primary-foreground" />
              </div>
              <span class="text-sm font-medium text-foreground">管理员</span>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <router-view />
      </main>
    </div>

    <!-- 移动端遮罩 -->
    <div
      v-if="sidebarOpen"
      @click="sidebarOpen = false"
      class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Menu, Bell, User, BarChart3, Gift, Settings, Plus, ChevronDown, List, Trophy, Shield, FileText, PanelLeftClose, PanelLeftOpen } from 'lucide-vue-next'
import Button from '@/components/ui/button.vue'

const sidebarOpen = ref(false)
const sidebarCollapsed = ref(false)
const activitySubmenuOpen = ref(true)

const toggleActivitySubmenu = () => {
  activitySubmenuOpen.value = !activitySubmenuOpen.value
}

const toggleSidebarCollapse = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  if (sidebarCollapsed.value) {
    activitySubmenuOpen.value = false
  } else {
    activitySubmenuOpen.value = true
  }
}

const menuItems = [
  { name: 'settings', path: '/settings', label: '系统设置', icon: Settings },
]
</script>