<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0" :class="{ '-translate-x-full': !sidebarOpen }">
      <div class="flex items-center justify-center h-16 px-4 bg-gray-800">
        <h1 class="text-xl font-bold text-white">抽奖管理系统</h1>
      </div>
      
      <nav class="mt-8">
        <div class="px-4 space-y-2">
          <!-- 仪表盘 -->
          <router-link
            to="/"
            class="flex items-center px-4 py-3 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            :class="{ 'bg-gray-800 text-white border-r-2 border-blue-500': $route.path === '/' }"
          >
            <BarChart3 class="w-5 h-5 mr-3" />
            仪表盘
          </router-link>
          
          <!-- 活动管理子菜单 -->
          <div class="mt-2">
            <div 
              @click="toggleActivitySubmenu"
              class="flex items-center justify-between px-4 py-3 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors cursor-pointer"
              :class="{ 'bg-gray-800 text-white': $route.path.startsWith('/activities') }"
            >
              <div class="flex items-center">
                <Gift class="w-5 h-5 mr-3" />
                <span>活动管理</span>
              </div>
              <ChevronDown 
                class="w-4 h-4 transition-transform duration-200" 
                :class="{ 'rotate-180': activitySubmenuOpen }"
              />
            </div>
            
            <!-- 子菜单 -->
            <div v-show="activitySubmenuOpen" class="ml-4 mt-2 space-y-1">
              <router-link
                to="/activities"
                class="flex items-center px-4 py-2 text-gray-400 rounded-lg hover:bg-gray-800 transition-colors text-sm"
                :class="{ 'bg-gray-800 text-white': $route.path === '/activities' }"
              >
                <List class="w-4 h-4 mr-3" />
                活动列表
              </router-link>
              <router-link
                to="/activities/create"
                class="flex items-center px-4 py-2 text-gray-400 rounded-lg hover:bg-gray-800 transition-colors text-sm"
                :class="{ 'bg-gray-800 text-white': $route.path === '/activities/create' }"
              >
                <Plus class="w-4 h-4 mr-3" />
                创建活动
              </router-link>
            </div>
          </div>
          
          <!-- 参与记录 -->
          <router-link
            to="/participation-records"
            class="flex items-center px-4 py-3 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            :class="{ 'bg-gray-800 text-white border-r-2 border-blue-500': $route.path === '/participation-records' }"
          >
            <FileText class="w-5 h-5 mr-3" />
            参与记录
          </router-link>

          <!-- 中奖列表 -->
          <router-link
            to="/winners"
            class="flex items-center px-4 py-3 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            :class="{ 'bg-gray-800 text-white border-r-2 border-blue-500': $route.path === '/winners' }"
          >
            <Trophy class="w-5 h-5 mr-3" />
            中奖列表
          </router-link>

          <!-- 黑名单管理 -->
          <router-link
            to="/blacklist"
            class="flex items-center px-4 py-3 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            :class="{ 'bg-gray-800 text-white border-r-2 border-blue-500': $route.path === '/blacklist' }"
          >
            <Shield class="w-5 h-5 mr-3" />
            黑名单管理
          </router-link>

          <!-- 其他菜单项 -->
          <router-link
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            class="flex items-center px-4 py-3 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
            :class="{ 'bg-gray-800 text-white border-r-2 border-blue-500': $route.path === item.path }"
          >
            <component :is="item.icon" class="w-5 h-5 mr-3" />
            {{ item.label }}
          </router-link>
        </div>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:pl-64">
      <!-- 顶部导航栏 -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-4">
          <button
            @click="sidebarOpen = !sidebarOpen"
            class="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          >
            <Menu class="w-6 h-6" />
          </button>
          
          <div class="flex items-center space-x-4 ml-auto">
            <div class="relative">
              <Bell class="w-6 h-6 text-gray-600 cursor-pointer hover:text-gray-900" />
              <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <User class="w-5 h-5 text-white" />
              </div>
              <span class="text-sm font-medium text-gray-700">管理员</span>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <router-view />
      </main>
    </div>

    <!-- 移动端遮罩 -->
    <div
      v-if="sidebarOpen"
      @click="sidebarOpen = false"
      class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Menu, Bell, User, BarChart3, Gift, Users, History, Settings, Plus, ChevronDown, List, Trophy, Shield, FileText } from 'lucide-vue-next'

const sidebarOpen = ref(false)
const activitySubmenuOpen = ref(true)

const toggleActivitySubmenu = () => {
  activitySubmenuOpen.value = !activitySubmenuOpen.value
}

const menuItems = [
  { name: 'users', path: '/users', label: '用户管理', icon: Users },
  { name: 'settings', path: '/settings', label: '系统设置', icon: Settings },
]
</script>